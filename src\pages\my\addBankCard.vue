<template>
  <view class="add-bank-card-page">
    <!-- 步骤1：首次绑定 - 基本信息和设置支付密码 -->
    <view v-if="currentStep === 1" class="step-content">
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-row">
          <text class="form-label">
            <text class="label-text">姓名</text>
          </text>
          <text class="form-value">{{ "12121" }}</text>
        </view>

        <!-- 手机号 -->
        <view class="form-row">
          <text class="form-label">
            <text class="label-text">手机号</text>
          </text>
          <text class="form-value">{{ "12121" }}</text>
        </view>

        <!-- 设置支付密码 -->
        <view class="form-row">
          <text class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">支付密码</text>
          </text>
          <view class="form-value" @click="handleSetPayPassword">
            <view class="password-display">
              <text v-if="!hasPayPassword" class="password-placeholder"
                >请设置支付密码</text
              >
              <view v-else class="password-dots">
                <text class="dot" v-for="n in 6" :key="n">*</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 下一步按钮 -->
      <view class="bottom-fixed">
        <button
          class="confirm-btn"
          :class="{ disabled: !hasPayPassword }"
          :disabled="!hasPayPassword"
          @click="handleNext"
        >
          下一步
        </button>
      </view>
    </view>

    <!-- 步骤2：银行卡信息表单 -->
    <view v-if="currentStep === 2" class="step-content">
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-row" @click="focusInput('name')">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">姓名</text>
          </view>
          <input
            ref="nameInput"
            v-model="bankCardInfo.name"
            placeholder="请输入持卡人姓名"
            class="form-input"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </view>

        <!-- 身份证号 -->
        <view class="form-row" @click="focusInput('idCard')">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">身份证号码</text>
          </view>
          <input
            ref="idCardInput"
            v-model="bankCardInfo.idCard"
            placeholder="请输入身份证号码"
            maxlength="18"
            class="form-input"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </view>

        <!-- 开户银行 -->
        <view class="form-row">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">开户银行</text>
          </view>
          <view class="form-picker-container">
            <uni-data-picker
              v-model="bankCardInfo.bankCode"
              :localdata="bankOptions"
              placeholder="请选择银行"
              popup-title="选择开户银行"
              @change="onBankChange"
            />
          </view>
        </view>

        <!-- 银行卡类型 -->
        <view class="form-row">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">银行卡类型</text>
          </view>
          <view class="form-picker-container">
            <uni-data-picker
              v-model="bankCardInfo.cardType"
              :localdata="cardTypeOptions"
              placeholder="储蓄卡"
              popup-title="选择卡类型"
              @change="onCardTypeChange"
            />
          </view>
        </view>

        <!-- 银行卡号 -->
        <view class="form-row" @click="focusInput('cardNumber')">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">银行卡号</text>
          </view>
          <input
            ref="cardNumberInput"
            v-model="bankCardInfo.cardNumber"
            placeholder="请输入银行卡号"
            type="number"
            maxlength="19"
            class="form-input"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </view>

        <!-- 手机号 -->
        <view class="form-row" @click="focusInput('phone')">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">手机号码</text>
          </view>
          <input
            ref="phoneInput"
            v-model="bankCardInfo.phone"
            placeholder="请输入银行卡绑定的手机号码"
            type="number"
            maxlength="11"
            class="form-input"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </view>

        <!-- 短信验证码 -->
        <view class="form-row sms-row">
          <view class="form-label">
            <text class="required-star">*</text>
            <text class="label-text">短信验证码</text>
          </view>
          <view class="sms-input-container">
            <input
              ref="smsCodeInput"
              v-model="bankCardInfo.smsCode"
              placeholder="请输入验证码"
              type="number"
              maxlength="6"
              class="form-input sms-input"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
            />
            <button
              class="sms-btn"
              :class="{ disabled: !canSendSms || smsCountdown > 0 }"
              :disabled="!canSendSms || smsCountdown > 0"
              @click="handleSendSms"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : "发送" }}
            </button>
          </view>
        </view>

        <!-- 协议勾选 -->
        <view class="agreement-section">
          <view class="agreement-row" @click="toggleAgreement">
            <view class="checkbox-container">
              <view
                class="custom-checkbox"
                :class="{ checked: isAgreementChecked }"
              >
                <uni-icons
                  v-if="isAgreementChecked"
                  type="checkmarkempty"
                  size="16"
                  color="#FFFFFF"
                ></uni-icons>
              </view>
            </view>
            <view class="agreement-text">
              <text class="agreement-normal">我已阅读并同意</text>
              <text class="agreement-link" @click.stop="showAgreement"
                >《线上支付服务协议》</text
              >
            </view>
          </view>
          <view class="warning-text">
            <text class="warning-label">温馨提醒：</text>
            <text class="warning-content"
              >为了您能正常使用本系统，请核实并确保以上信息无误。</text
            >
          </view>
        </view>
      </view>

      <!-- 底部固定按钮 -->
      <view class="bottom-fixed">
        <button
          class="confirm-btn"
          :class="{ disabled: !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          确定
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from "vue";
import { useSafeStorage } from "@/hooks/useSafeStorage";
import http from "@/utils/request";

// 响应式数据
const currentStep = ref(2); // 当前步骤：1-基本信息，2-银行卡信息
const isFirstBinding = ref(true); // 是否首次绑定
const hasPayPassword = ref(false); // 是否已设置支付密码
const smsCountdown = ref(0); // 短信倒计时
const isAgreementChecked = ref(false); // 协议勾选状态

// 用户基本信息
const userInfo = reactive({
  name: "",
  phone: "",
});

// 银行卡信息
const bankCardInfo = reactive({
  name: "",
  idCard: "",
  bankCode: "",
  cardType: "",
  cardNumber: "",
  phone: "",
  smsCode: "",
});

// 获取银行卡列表
const getBanklist =() => {
  http
    .post("/app/enrollment/bocom/bank-card/bank-list",{})
    .then((res) => {
      console.log(res, "拿到了可进行支付的列表信息进行渲染");
    });

}


// 银行选项
const bankOptions = reactive([
  { value: "ICBC", text: "中国工商银行" },
  { value: "ABC", text: "中国农业银行" },
  { value: "BOC", text: "中国银行" },
  { value: "CCB", text: "中国建设银行" },
  { value: "COMM", text: "交通银行" },
  { value: "CMB", text: "招商银行" },
  { value: "CITIC", text: "中信银行" },
  { value: "CEB", text: "光大银行" },
  { value: "CMBC", text: "中国民生银行" },
  { value: "PAB", text: "平安银行" },
  { value: "PAB", text: "平安银行" },
  { value: "PAB", text: "平安银行" },
  { value: "PAB", text: "平安银行" },
  { value: "PAB", text: "平安银行" },
  { value: "PAB", text: "平安银行" },
]);

// 银行卡类型选项
const cardTypeOptions = reactive([
  { value: "debit", text: "储蓄卡" },
  { value: "credit", text: "信用卡" },
]);

// 协议选项
const agreementOptions = reactive([{ value: "agree", text: "" }]);

const canGoNext = computed(() => {
  return hasPayPassword.value;
});

const canSendSms = computed(() => {
  return bankCardInfo.phone.trim() && /^1[3-9]\d{9}$/.test(bankCardInfo.phone);
});

const canSubmit = computed(() => {
  return (
    bankCardInfo.name.trim() &&
    bankCardInfo.idCard.trim() &&
    bankCardInfo.bankCode &&
    bankCardInfo.cardType &&
    bankCardInfo.cardNumber.trim() &&
    bankCardInfo.phone.trim() &&
    bankCardInfo.smsCode.trim() &&
    isAgreementChecked.value
  );
});

/**
 * 检查是否已绑定银行卡
 */
const checkExistingBankCards = async () => {
  try {
    // 获取localStrorage参数
    const loginInfo = useSafeStorage("checkIn-loginInfo", {});
    if (loginInfo.value.isPassword) {
      isFirstBinding.value = false;
      currentStep.value = 2;
      hasPayPassword.value = true;
      return;
    } else {
      isFirstBinding.value = true;
      currentStep.value = 1;
      hasPayPassword.value = false;
    }
  } catch (error) {
    console.error("检查银行卡失败:", error);
  }
};

/**
 * 设置支付密码
 */
const handleSetPayPassword = () => {
  // 跳转到设置支付密码页面
  uni.navigateTo({
    url: "/pages/my/payPasswordSetting?from=addBankCard",
    success: () => {
      console.log("跳转到设置支付密码页面");
    },
    fail: (error) => {
      console.error("跳转失败:", error);
      uni.showToast({
        title: "页面跳转失败",
        icon: "none",
      });
    },
  });
};

/**
 * 下一步
 */
const handleNext = () => {
  if (!canGoNext.value) return;

  // 将基本信息复制到银行卡信息
  bankCardInfo.name = userInfo.name;
  bankCardInfo.phone = userInfo.phone;

  currentStep.value = 2;
};

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  if (!canSendSms.value || smsCountdown.value > 0) return;

  try {
    // 模拟发送短信API
    await new Promise((resolve) => setTimeout(resolve, 1000));

    uni.showToast({
      title: "验证码已发送",
      icon: "success",
    });

    // 开始倒计时
    smsCountdown.value = 60;
    const timer = setInterval(() => {
      smsCountdown.value--;
      if (smsCountdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error) {
    console.error("发送短信失败:", error);
    uni.showToast({
      title: "发送失败，请重试",
      icon: "none",
    });
  }
};

/**
 * 聚焦输入框
 */
const focusInput = (inputName) => {
  // 通过ref聚焦对应的输入框
  const inputRef = `${inputName}Input`;
  // 这里可以添加聚焦逻辑，但在H5中input会自动聚焦
};

/**
 * 输入框聚焦处理
 */
const handleInputFocus = () => {
  // 输入框聚焦时的处理
};

/**
 * 输入框失焦处理
 */
const handleInputBlur = () => {
  // 输入框失焦时的处理
};

/**
 * 银行选择变化
 */
const onBankChange = (e) => {
  console.log("银行选择变化:", e);
  bankCardInfo.bankCode = e;
};

/**
 * 卡类型选择变化
 */
const onCardTypeChange = (e) => {
  console.log("卡类型选择变化:", e);
  bankCardInfo.cardType = e;
};

/**
 * 切换协议勾选状态
 */
const toggleAgreement = () => {
  isAgreementChecked.value = !isAgreementChecked.value;
};

/**
 * 显示协议
 */
const showAgreement = () => {
  uni.showModal({
    title: "银行卡绑定协议",
    content: "这里是银行卡绑定协议的详细内容...",
    showCancel: false,
  });
};

/**
 * 提交银行卡信息
 */
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  try {
    uni.showLoading({
      title: "绑定中...",
    });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000));

    uni.hideLoading();
    uni.showToast({
      title: "银行卡绑定成功",
      icon: "success",
    });

    // 延迟返回
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error("绑定银行卡失败:", error);
    uni.hideLoading();
    uni.showToast({
      title: "绑定失败，请重试",
      icon: "none",
    });
  }
};

/**
 * 返回处理
 */
const handleBack = () => {
  if (currentStep.value === 2 && isFirstBinding.value) {
    // 从步骤2返回步骤1
    currentStep.value = 1;
  } else {
    // 退出页面
    uni.navigateBack();
  }
};

/**
 * 支付密码设置成功回调
 */
const onPayPasswordSetSuccess = () => {
  hasPayPassword.value = true;
  console.log("支付密码设置成功，更新状态!!!!!!!!!!!!!!!!!!");
};

// 页面挂载时检查
onMounted(() => {
  // checkExistingBankCards();

  // 监听支付密码设置成功事件
  uni.$on("payPasswordSetSuccess", onPayPasswordSetSuccess);
});

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off("payPasswordSetSuccess", onPayPasswordSetSuccess);
});

// 全部返回卡列表页面
onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/bankList",
  });
  return true;
});









onShow(() => {
  const loginInfo = useSafeStorage("checkIn-loginInfo", {});
  if (!loginInfo.value.reportId) {
    uni.reLaunch({
      url: "/pages/home/<USER>",
    });
  }
  getBanklist()
});




</script>

<style lang="scss" scoped>
.add-bank-card-page {
  height: calc(100vh - var(--window-top));
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 步骤内容 */
.step-content {
  flex: 1;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  /* 表单行 */
  .form-row {
    display: flex;
    align-items: center;
    padding-top: 28rpx;
    padding-bottom: 28rpx;
    border-bottom: 1rpx solid #d8d8d8;
    margin-left: 30rpx;
    margin-right: 30rpx;

    .form-label {
      display: flex;
      align-items: center;
      min-width: 200rpx;
      margin-right: 32rpx;

      .required-star {
        color: #ff4757;
        font-size: 28rpx;
        margin-right: 8rpx;
      }

      .label-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }
    }

    .form-value {
      text-align: right;
      flex: 1;
    }
    .form-input {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      border: none;
      outline: none;
      background: transparent;
      text-align: right;

      :deep(.uni-input-placeholder) {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.25);
      }
    }

    .form-picker-container {
      flex: 1;
      :deep(.input-value-border) {
        border: none !important;
        border-radius: 0 !important;
      }
    }

    /* 短信验证码行特殊样式 */
    &.sms-row {
      .sms-input-container {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 24rpx;

        .sms-input {
          flex: 1;
        }

        .sms-btn {
          width: 120rpx;
          height: 60rpx;
          background-color: #00b781;
          color: #ffffff;
          font-size: 24rpx;
          border-radius: 8rpx;
          border: none;

          &.disabled {
            background-color: #cccccc;
            color: #999999;
          }

          &:active:not(.disabled) {
            background-color: #00b781;
          }
        }
      }
    }
  }

  /* 协议区域 */
  .agreement-section {
    padding: 32rpx;
    border-top: 20rpx solid #f7f7f7;

    .agreement-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      .checkbox-container {
        margin-right: 16rpx;
        margin-top: 4rpx;

        .custom-checkbox {
          width: 32rpx;
          height: 32rpx;
          border: 2rpx solid #cccccc;
          border-radius: 4rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #ffffff;

          &.checked {
            background-color: #00b781;
            border-color: #00b781;
          }
        }
      }

      .agreement-text {
        flex: 1;
        line-height: 1.6;

        .agreement-normal {
          font-size: 26rpx;
          color: #666666;
        }

        .agreement-link {
          font-size: 26rpx;
          color: #00b781;
        }
      }
    }

    .warning-text {
      .warning-label {
        font-size: 24rpx;
        color: #ff4757;
      }

      .warning-content {
        font-size: 24rpx;
        color: #ff4757;
        line-height: 1.5;
      }
    }
  }
}

/* 底部固定按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background-color: #00b781;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 44rpx;
    border: none;

    &.disabled {
      background-color: #cccccc;
      color: #999999;
    }

    &:active:not(.disabled) {
      background-color: #00b781;
    }
  }
}

/* uni-data-picker 样式调整 */
:deep(.uni-data-picker) {
  .uni-data-picker__input-box {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    height: auto !important;

    .uni-data-picker__input {
      font-size: 28rpx !important;
      color: #333333 !important;
      text-align: right !important;

      &.uni-data-picker__input--placeholder {
        color: #cccccc !important;
      }
    }

    .uni-data-picker__input-arrow {
      color: #cccccc !important;
      font-size: 16rpx !important;
    }
  }
}

.password-placeholder {
  font-weight: 400;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.25);
}

/* 为底部固定按钮留出空间 */
.step-content {
  padding-bottom: 200rpx;
}
</style>
