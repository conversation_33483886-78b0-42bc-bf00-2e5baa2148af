<template>
  <view class="refund-container">
    <view class="bgLine"></view>
    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 订单卡片列表 -->

      <view class="order-card_box">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-type">
            <text class="type-icon"></text>
            <text class="type-text">{{ state.details.title }}</text>
          </view>
          <view>
            <!-- 支付的状态 -->
            <view
              style="padding-bottom: 8rpx"
              class="order-status"
              :style="{ color: getStatus(state.details.orderStatus)?.color }"
            >
              {{ getStatus(state.details.orderStatus)?.text }}
            </view>
            <!-- 退款相关的状态 -->
            <view
              class="order-status"
              :style="{
                color: getRefundStatus(state.details.orderStatus)?.color,
              }"
              v-if="getRefundStatus(state.details.orderStatus)"
            >
              {{ getRefundStatus(state.details.orderStatus)?.text }}</view
            >
          </view>
        </view>

        <!-- 订单标题 -->
        <view class="order-title">{{ state.details.title }}</view>

        <!-- 订单详情 -->
        <view class="order-details">
          <view class="detail-row">
            <text class="detail-label">收款方:</text>
            <text class="detail-value">{{ state.details.title || "-" }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">内部订单编号:</text>
            <text class="detail-value">{{ 234 || "-" }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">交易流水号:</text>
            <text class="detail-value">{{ 234 || "-" }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">创建时间:</text>
            <text class="detail-value">{{ 234 || "-" }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">支付时间:</text>
            <text class="detail-value amount">¥{{ 234 }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">应缴金额:</text>
            <text class="detail-value amount">¥{{ 234 }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">减免金额:</text>
            <text class="detail-value amount">¥{{ 234 }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">实缴金额:</text>
            <text class="detail-value amount">¥{{ 234 }}</text>
          </view>
        </view>
      </view>

      <view class="yuanyin">
        <view class="yuanyin_title"
          ><text class="yuanyin_red">*</text>退款原因</view
        >
        <uni-easyinput
          :inputBorder="false"
          type="textarea"
          v-model="state.refundReason"
          placeholder="请输入退款原因"
        ></uni-easyinput>
      </view>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bottom-button">
      <button class="next-btn" @click="submitRefund">确定</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted, reactive } from "vue";
import http from "@/utils/request";
import { onLoad } from "@dcloudio/uni-app";
import { onShow } from "@dcloudio/uni-app";
import { useSafeStorage } from "@/hooks/useSafeStorage";
const loginInfo = useSafeStorage("checkIn-loginInfo", {});
const detailId = ref("");

const state = reactive({
  details: {},
});

const refundStatusMap = {
  7: {
    text: "退款成功",
    color: "#00D190",
  },
  3: {
    text: "审核中",
    color: "#FAAD14",
  },
  5: {
    text: "退款失败",
    color: "#FF1C1C",
  },
};

const getRefundStatus = (orderStatus) => {
  return refundStatusMap[orderStatus];
};

const statusMap = {
  0: {
    text: "待支付",
    color: "#FF7F00",
  },
  1: {
    text: "待支付",
    color: "#FF7F00",
  },
  2: {
    text: "已支付",
    color: "#00D190",
  },
  3: {
    text: "已支付",
    color: "#00D190",
  },
  4: {
    text: "已支付",
    color: "#00D190",
  },
  5: {
    text: "已支付",
    color: "#00D190",
  },
  6: {
    text: "已关闭",
    color: "#999999",
  },
  7: {
    text: "已关闭",
    color: "#999999",
  },
};

const getStatus = (orderStatus) => {
  return statusMap[orderStatus];
};

// 查询订单列表
const queryDetails = async () => {
  try {
    const response = await http.post(
      "/campuspay/mobile/general-pay-order/details",
      {
        id: detailId.value,
      }
    );
    state.details = response.data;
  } catch (error) {
    console.error("查询订单列表失败:", error);
  }
};

onLoad((options) => {
  detailId.value = options.id;
});

onShow(() => {
  queryDetails();
});

const submitRefund = () => {
  http
    .post("/campuspay/mobile/general-pay-order/applyRefund", {
      orderUserId: loginInfo.value.reportId,
      orderNo: state.details.orderNo,
      refundReason: state.refundReason,
    })
    .then(() => {
      uni.showToast({
        title: "申请退款成功",
        icon: "none",
        duration: 2000,
      });
    });
};
</script>

<style lang="scss" scoped>
.refund-container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
}

.bgLine {
  height: 26rpx;
  background: #f9faf9;
}

.content {
  flex: 1;
}

.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.next-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 适配安全区域 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .bottom-button {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}

.order-record-page {
  min-height: 100vh;
  background-color: #f9faf9;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottomBtn {
  background-color: #ffffff;
  height: 100rpx;
  padding: 20rpx;
  border-top: 2rpx solid #ebebeb;
  display: flex;
  gap: 30rpx;
  justify-content: flex-end;
}

.cancelBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  border: 2rpx solid #d1d1d1;
  font-weight: 400;
  font-size: 24rpx;
  color: #d1d1d1;
  background: #ffffff;
}

.payBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  background: #11c685;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
}

.order-card {
  background: #ffffff;
}

.order-card_box {
  padding: 32rpx;
}
.payee_order-card {
  background: #ffffff;
  padding: 32rpx;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 34rpx;
}

.order-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
}

.type-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 订单详情 */
.order-details {
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
}

.payee_order-details {
  margin-bottom: 32rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.payee_detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.payee_detail-label {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.payee_detail-value {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
  text-align: right;
}

/* z-paging组件样式覆盖 */
:deep(.zp-paging-container) {
  background-color: #ffffff;
}

:deep(.zp-paging-container-content) {
  background-color: #ffffff;
}

.popup-content {
  height: 660rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.handle-card {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.handle-card-close {
  padding: 30rpx;
}

.card-List {
  padding-left: 60rpx;
  padding-right: 60rpx;
}
.cardItem {
  display: flex;
  padding-bottom: 30rpx;
  align-items: center;
  justify-content: space-between;
}

.bottom_btn {
  padding-left: 60rpx;
  padding-right: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bottom_del_btn {
  background: #ffffff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #dfdfdf;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  width: 172rpx;
  height: 70rpx;
}

.bgLine {
  height: 20rpx;
  background: #f6f5f8;
}

.amount {
  font-weight: 600;
  font-size: 40rpx;
  color: #333333;
}

.order-title {
  font-weight: 600;
  font-size: 36rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}

.order-time {
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}

.yuanyin {
  padding: 30rpx;
  border-top: 2rpx solid #ebebeb;
}

.yuanyin_title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.yuanyin_red {
  color: #ff0000;
}
</style>
