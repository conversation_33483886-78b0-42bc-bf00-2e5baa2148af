<template>
  <view class="order-record-page">
    <!-- z-paging组件，铺满全屏 -->
    <z-paging
      ref="pagingRef"
      v-model="orderList"
      :fixed="true"
      :auto="true"
      :default-page-size="10"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="true"
      @query="queryOrderList"
      @refresherrefresh="onRefresh"
    >
      <!-- 顶部固定区域：搜索和筛选 -->
      <template #top>
        <view class="search-filter-section">
          <!-- 搜索框 -->
          <view class="search-container">
            <uni-search-bar
              v-model="searchKeyword"
              placeholder="请输入缴费项目"
              :radius="30"
              :focus="false"
              :show-action="false"
              clearButton="always"
              cancelButton="none"
              @input="onSearchInput"
              @clear="onSearchClear"
            />
          </view>

          <!-- 筛选器 -->
          <view class="filter-container">
            <!--缴费状态筛选 -->
            <view class="filter-item" @click="togglequeryStatusFilter">
              <text class="filter-label">缴费状态</text>
              <uni-icons
                :type="showqueryStatusFilter ? 'up' : 'down'"
                size="14"
                color="#666666"
              />
            </view>

            <!-- 成功缴费时间筛选 -->
            <view class="filter-item" @click="toggleTimeRangeFilter">
              <text class="filter-label">成功缴费时间</text>
              <uni-icons
                :type="showTimeRangeFilter ? 'up' : 'down'"
                size="14"
                color="#666666"
              />
            </view>
          </view>

          <!--缴费状态选择器 -->
          <view v-if="showqueryStatusFilter" class="filter-dropdown">
            <uni-data-select
              v-model="filterData.queryStatus"
              :localdata="queryStatusOptions"
              placeholder="请选择缴费状态"
              :clear="true"
              @change="onqueryStatusChange"
            />
          </view>

          <!-- 时间范围选择器 -->
          <view v-if="showTimeRangeFilter" class="filter-dropdown">
            <uni-datetime-picker
              v-model="filterData.timeRange"
              type="daterange"
              :border="false"
              placeholder="请选择时间范围"
              :clear-icon="true"
              @change="onTimeRangeChange"
            />
          </view>
        </view>
      </template>
      <!-- 订单卡片列表 -->
      <view class="order-card-list">
        <view
          class="order-card"
          v-for="item in orderList"
          :key="item.id"
          @click="goDetails(item)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-type">
              <text class="type-icon"></text>
              <text class="type-text">{{ item.title }}</text>
            </view>

            <view>
              <!-- 支付的状态 -->
              <view
                style="padding-bottom: 8rpx"
                class="order-status"
                :style="{ color: getStatus(item.orderStatus).color }"
              >
                {{ getStatus(item.orderStatus).text }}
              </view>
              <!-- 退款相关的状态 -->
              <view
                class="order-status"
                :style="{ color: getRefundStatus(item.orderStatus).color }"
                v-if="getRefundStatus(item.orderStatus)"
              >
                {{ getRefundStatus(item.orderStatus).text }}</view
              >
            </view>
          </view>

          <!-- 订单标题 -->
          <view class="order-title">{{ item.title }}</view>

          <!-- 订单标题 -->
          <view class="order-time">缴费时间:{{ item.payEndTime }}</view>

          <!-- 订单详情 -->
          <view class="order-details">
            <view class="detail-row">
              <text class="detail-label">缴费细项:</text>
              <text class="detail-value">{{ item.title || "-" }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">备注说明:</text>
              <text class="detail-value">{{ item.title || "-" }}</text>
            </view>

            <view class="detail-row">
              <text class="detail-label">应缴金额:</text>
              <text class="detail-value">{{ item.payableAmount || "-" }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">减免金额:</text>
              <text class="detail-value">{{
                item.payableAmount - item.payAmount || "-"
              }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">实缴金额:</text>
              <text class="detail-value amount"
                >¥{{ item.payAmount || "0.00" }}</text
              >
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="order-actions">
            <view
              class="action-btn secondary"
              v-if="item.orderStatus === 0 || item.orderStatus === 1"
              @click.stop="cancelOrder(item)"
            >
              取消订单
            </view>
            <view
              v-if="
                item.orderStatus === 2 ||
                item.orderStatus === 5 ||
                item.orderStatus === 6 ||
                item.orderStatus === 7
              "
              class="action-btn secondary"
              @click.stop="delOrder(item)"
            >
              删除订单
            </view>
            <view
              v-if="item.orderStatus === 3"
              class="action-btn secondary"
              @click.stop="cancelRefund(item)"
            >
              取消申请
            </view>
            <view
              v-if="item.orderStatus === 0 || item.orderStatus === 1"
              class="action-btn primary"
              @click.stop="payOrder(item)"
            >
              立即支付
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <MergPay ref="isMergePopup"></MergPay>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";

import MergPay from "@/components/MergPay.vue";

import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";
const loginInfo = useSafeStorage("checkIn-loginInfo", {});

const isMergePopup = ref(null);
// 响应式数据
const searchKeyword = ref("");
const orderList = ref([]);
const pagingRef = ref(null);

// 筛选器展开状态
const showqueryStatusFilter = ref(false);
const showTimeRangeFilter = ref(false);

// 搜索定时器
let searchTimer = null;

// 筛选数据
const filterData = reactive({
  queryStatus: "",
  timeRange: [],
});

//缴费状态选项
const queryStatusOptions = ref([
  { value: 0, text: "未支付" },
  { value: 1, text: "已支付" },
  { value: 2, text: "退款" },
  { value: 3, text: "关闭" },
]);

const refundStatusMap = {
  7: {
    text: "退款成功",
    color: "#00D190",
  },
  3: {
    text: "审核中",
    color: "#FAAD14",
  },
  5: {
    text: "退款失败",
    color: "#FF1C1C",
  },
};

const getRefundStatus = (orderStatus) => {
  return refundStatusMap[orderStatus];
};

const statusMap = {
  0: {
    text: "待支付",
    color: "#FF7F00",
  },
  1: {
    text: "待支付",
    color: "#FF7F00",
  },
  2: {
    text: "已支付",
    color: "#00D190",
  },
  3: {
    text: "已支付",
    color: "#00D190",
  },
  4: {
    text: "已支付",
    color: "#00D190",
  },
  5: {
    text: "已支付",
    color: "#00D190",
  },
  6: {
    text: "已关闭",
    color: "#999999",
  },
  7: {
    text: "已关闭",
    color: "#999999",
  },
};

const getStatus = (orderStatus) => {
  return statusMap[orderStatus];
};

// 查询订单列表
const queryOrderList = async (pageNo, pageSize) => {
  try {
    const params = {
      pageNo,
      pageSize,
      orderUserId: loginInfo.value.reportId,
      businessType: 4,
      keyword: searchKeyword.value,
      queryStatus: filterData.queryStatus,
      crateTime: filterData.timeRange[0] || "",
      endTime: filterData.timeRange[1] || "",
    };

    const response = await http.post(
      "/campuspay/mobile/general-pay-order/page",
      params
    );

    // 使用z-paging的complete方法完成数据加载
    pagingRef.value.complete(response.data.list);
  } catch (error) {
    console.error("查询订单列表失败:", error);
    pagingRef.value.complete([]);
  }
};

// 切换缴费状态筛选器
const togglequeryStatusFilter = () => {
  showqueryStatusFilter.value = !showqueryStatusFilter.value;
  // 关闭其他筛选器
  if (showqueryStatusFilter.value) {
    showTimeRangeFilter.value = false;
  }
};

// 切换时间范围筛选器
const toggleTimeRangeFilter = () => {
  showTimeRangeFilter.value = !showTimeRangeFilter.value;
  // 关闭其他筛选器
  if (showTimeRangeFilter.value) {
    showqueryStatusFilter.value = false;
  }
};

// 搜索输入处理
const onSearchInput = (value) => {
  searchKeyword.value = value;
  // 延迟搜索，避免频繁请求
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    refreshData();
  }, 500);
};

// 搜索清除处理
const onSearchClear = () => {
  searchKeyword.value = "";
  refreshData();
};

//缴费状态变化处理
const onqueryStatusChange = (value) => {
  filterData.queryStatus = value;
  showqueryStatusFilter.value = false; // 选择后自动收起
  refreshData();
};

// 时间范围变化处理
const onTimeRangeChange = (value) => {
  filterData.timeRange = value;
  showTimeRangeFilter.value = false; // 选择后自动收起
  refreshData();
};

// 下拉刷新处理
const onRefresh = () => {
  refreshData();
};

// 刷新数据
const refreshData = () => {
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

const goDetails = (item) => {
  uni.navigateTo({
    url: `/pages/my/orderDetails?id=${item.id}`,
  });
};

// 取消订单
const cancelOrder = (item) => {
  isMergePopup.value.showIsMergePopup(item.tradeNo, "nopay");
};

// 支付订单
const payOrder = (item) => {
  isMergePopup.value.showIsMergePopup(item.tradeNo, "pay");
};

const delOrder = (item) => {
  http
    .post("/campuspay/mobile/general-pay-order/delete/order", {
      id: item.id,
    })
    .then(() => {
      uni.showToast({
        title: "删除订单成功",
        icon: "none",
        duration: 2000,
      });
    });
};

const cancelRefund = (item) => {
  http
    .post("/campuspay/mobile/general-pay-order/cancel/apply", {
      id: item.id,
    })
    .then(() => {
      uni.showToast({
        title: "取消申请成功",
        icon: "none",
        duration: 2000,
      });
    });
};
</script>

<style scoped>
/* 页面容器 */
.order-record-page {
  min-height: 100vh;
  background-color: #f9faf9;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 搜索和筛选区域 */
.search-filter-section {
  background-color: #ffffff;

  border-bottom: 1rpx solid #f0f0f0;
}

/* 搜索容器 */
.search-container {
  border-bottom: 2rpx solid #ebebeb;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 筛选容器 */
.filter-container {
  display: flex;

  justify-content: space-between;

  padding: 32rpx 54rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;

  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-label {
  font-size: 28rpx;
  color: #333333;
}

/* 筛选下拉框 */
.filter-dropdown {
  padding: 16rpx;
  background-color: #ffffff;
}

.order-card-list {
  margin: 32rpx;
}

.order-card-list .order-card:first-child {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.order-card-list .order-card:last-child {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}

/* 订单卡片 */
.order-card {
  background: #ffffff;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebebeb;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.order-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
}

.type-text {
  font-weight: 600;
  font-size: 28rpx;
  color: #4d4d4d;
}

.order-status {
  font-weight: 600;
  font-size: 24rpx;
}

/* 订单标题 */
.order-title {
  font-weight: 600;
  font-size: 36rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}

.order-time {
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}

/* 订单详情 */
.order-details {
  margin-bottom: 32rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.amount {
  font-weight: 600;
  font-size: 40rpx;
  color: #333333;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 400;
  font-size: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #ffffff;
  color: #666666;
  border: 2rpx solid #dfdfdf;
}

.action-btn.primary {
  background-color: #00b781;
  color: #ffffff;
  border: 1rpx solid #00b781;
}

:deep(.uni-data-select) {
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #ffffff;
}

:deep(.uni-select__input-text) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.uni-select__input-placeholder) {
  color: #999999;
  font-size: 28rpx;
}

:deep(.uni-datetime-picker) {
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #ffffff;
}

:deep(.uni-datetime-picker .uni-datetime-picker-view) {
  border: none;
  background-color: transparent;
}

:deep(.uni-datetime-picker .uni-datetime-picker-text) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.uni-datetime-picker .uni-datetime-picker-placeholder) {
  color: #999999;
  font-size: 28rpx;
}

/* z-paging组件样式覆盖 */
:deep(.zp-paging-container) {
  background-color: #f9faf9;
}

:deep(.zp-paging-container-content) {
  background-color: #f9faf9;
}
</style>
