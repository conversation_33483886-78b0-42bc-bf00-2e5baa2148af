<template>
  <view class="setting">
    <view class="bgLine"></view>
    <view class="listPage">
      <view
        class="listItem"
        v-if="!!loginInfo.password"
        @click="handleModifyPayPassword"
      >
        <view class="listItem_name">
          <view class="item_title">修改支付密码</view>
        </view>
      </view>
      <view class="listItem" @click="handleLogout">
        <view class="listItem_name">
          <view class="item_title">退出登录</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useSafeStorage } from "@/hooks/useSafeStorage";
import { onShow } from "@dcloudio/uni-app";
const loginInfo = useSafeStorage("checkIn-loginInfo", {});
onShow(() => {
  console.log(loginInfo.value, "loginInfo");
  if (!loginInfo.value.reportId) {
    uni.reLaunch({
      url: "/pages/home/<USER>",
    });
  }
});

const handleLogout = () => {
  uni.removeStorageSync("checkIn-loginInfo");
  uni.reLaunch({
    url: "/pages/home/<USER>",
  });
};

const handleModifyPayPassword = () => {
  uni.navigateTo({
    url: "/pages/my/identity",
  });
};


onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/homePage",
  });
  return true;
});


</script>

<style scoped>
.setting {
  height: calc(100vh - var(--window-top));
  background-color: #f6f6f6;
}

.listPage {
  background: #ffffff;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.listItem {
  padding: 30rpx 0rpx 30rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e8e8e8;
}

.listItem_name {
  display: flex;
  align-items: center;
}

.item_title {
  padding-left: 16rpx;

  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

.bgLine {
  height: 20rpx;
  background: #f9faf9;
}
</style>
