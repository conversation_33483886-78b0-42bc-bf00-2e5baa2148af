
import http from "@/utils/request"

// import useStore from '@/store'
// const { user } = useStore()

export function login(data) {
    return http.post('/login', data).then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('登录失败', response)
            return Promise.reject(new Error("登录失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`Request Login failed: ${error}`))
        })
}

/**
 * 获取银行卡列表
 * @returns {Promise} 银行卡列表数据
 */
export function getBankCardList() {
    return http.get('/bankCard/list').then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('获取银行卡列表失败', response)
            return Promise.reject(new Error("获取银行卡列表失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`获取银行卡列表失败: ${error}`))
        })
}

/**
 * 解绑银行卡
 * @param {string} cardId - 银行卡ID
 * @returns {Promise} 解绑结果
 */
export function unbindBankCard(cardId) {
    return http.post('/bankCard/unbind', { cardId }).then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('解绑银行卡失败', response)
            return Promise.reject(new Error("解绑银行卡失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`解绑银行卡失败: ${error}`))
        })
}

/**
 * 验证支付密码
 * @param {string} password - 支付密码
 * @returns {Promise<boolean>} 验证结果
 */
export function verifyPayPasswordAPI(password) {
    return http.post('/user/verifyPayPassword', { password }).then((response) => {
        if (response && response.data) {
            return response.data.success || false
        } else {
            console.log('支付密码验证失败', response)
            return false
        }
    })
        .catch((error) => {
            console.log(error)
            return false
        })
}

/**
 * 设置支付密码
 * @param {string} password - 新的支付密码
 * @param {string} oldPassword - 旧密码（修改密码时需要）
 * @returns {Promise<boolean>} 设置结果
 */
export function setPayPasswordAPI(password, oldPassword = null) {
    const data = { password }
    if (oldPassword) {
        data.oldPassword = oldPassword
    }

    return http.post('/user/setPayPassword', data).then((response) => {
        if (response && response.data) {
            return response.data.success || false
        } else {
            console.log('设置支付密码失败', response)
            return false
        }
    })
        .catch((error) => {
            console.log(error)
            return false
        })
}

/**
 * 添加银行卡
 * @param {object} cardInfo - 银行卡信息
 * @returns {Promise<boolean>} 添加结果
 */
export function addBankCard(cardInfo) {
    return http.post('/bankCard/add', cardInfo).then((response) => {
        if (response && response.data) {
            return response.data.success || false
        } else {
            console.log('添加银行卡失败', response)
            return false
        }
    })
        .catch((error) => {
            console.log(error)
            return false
        })
}

/**
 * 发送短信验证码
 * @param {string} phone - 手机号
 * @param {string} type - 验证码类型
 * @returns {Promise<boolean>} 发送结果
 */
export function sendSmsCode(phone, type = 'bind_card') {
    return http.post('/sms/send', { phone, type }).then((response) => {
        if (response && response.data) {
            return response.data.success || false
        } else {
            console.log('发送短信失败', response)
            return false
        }
    })
        .catch((error) => {
            console.log(error)
            return false
        })
}



/**
 * 发送身份验证短信验证码
 * @param {string} phone - 手机号
 * @returns {Promise} 发送结果
 */
export function sendIdentityVerificationCode(phone) {
    return http.post('/app/enrollment/sms/otc/send', { phone }).then((response) => {
        if (response) {
            return response
        } else {
            console.log('发送身份验证短信失败', response)
            return Promise.reject(new Error("发送身份验证短信失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`发送身份验证短信失败: ${error}`))
        })
}

/**
 * 验证身份验证短信验证码
 * @param {string} phone - 手机号
 * @param {string} smsCode - 短信验证码
 * @returns {Promise} 验证结果
 */
export function verifyIdentityCode(phone, smsCode) {
    return http.post('/app/enrollment/sms/otc/verify', { phone, smsCode }).then((response) => {
        if (response) {
            return response
        } else {
            console.log('身份验证失败', response)
            return Promise.reject(new Error("身份验证失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`身份验证失败: ${error}`))
        })
}

